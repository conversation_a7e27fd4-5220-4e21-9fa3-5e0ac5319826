version: '3'
services:
  zookeeper:
    image: bitnami/zookeeper:3.9
    container_name: zookeeper
    ports:
      - "2181:2181"
    environment:
      ALLOW_ANONYMOUS_LOGIN: "yes"
    networks:
      - kafka-net

  kafka:
    image: bitnami/kafka:3.7
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_CFG_ZOOKEEPER_CONNECT: zookeeper:2181
      ALLOW_PLAINTEXT_LISTENER: yes
      KAFKA_CFG_LISTENERS: PLAINTEXT://:9092
      KAFKA_CFG_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_CFG_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_CFG_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    networks:
      - kafka-net

  kafka-sync-service:
    build:
      context: .
      dockerfile: KafkaSyncService/Dockerfile
    container_name: kafka-sync-service
    ports:
      - "8080:8080"
      - "5000:5000"  # Remote debugging port
    depends_on:
      - kafka
    environment:
      - KafkaSettings__BootstrapServers=kafka:9092
      - KafkaSettings__GroupId=ewp-sf-kafka-group
      - KafkaSettings__Topics__KafkaSync=kafka-sync-topic
      - KafkaSettings__Topics__KafkaUpdates=kafka-updates-topic
      - KafkaSettings__Consumer__MaxRetries=5
      - KafkaSettings__Consumer__InitialRetryDelayMs=2000
      - KafkaSettings__Consumer__AutoCreateTopics=true
      - ENABLE_REMOTE_DEBUG=true
      - VSTEST_HOST_DEBUG=1
      # Add this to make debugging more reliable
      - ASPNETCORE_ENVIRONMENT=Development
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/root/.microsoft/usersecrets:ro
      - ${APPDATA}/ASP.NET/Https:/root/.aspnet/https:ro
    networks:
      - kafka-net

networks:
  kafka-net:
    driver: bridge





