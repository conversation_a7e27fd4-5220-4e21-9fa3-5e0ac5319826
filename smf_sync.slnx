<Solution>
  <Folder Name="/SharedService/">
    <Project Path="SharedService/EWP.SF.Common/EWP.SF.Common.csproj" />
    <Project Path="SharedService/EWP.SF.Helper/EWP.SF.Helper.csproj" />
  </Folder>
  <Folder Name="/KafkaSyncService/">
    <Project Path="KafkaSyncService/EWP.SF.KafkaSync.API/EWP.SF.KafkaSync.API.csproj" />
    <Project Path="KafkaSyncService/EWP.SF.KafkaSync.BusinessEntities/EWP.SF.KafkaSync.BusinessEntities.csproj" />
    <Project Path="KafkaSyncService/EWP.SF.KafkaSync.BusinessLayer/EWP.SF.KafkaSync.BusinessLayer.csproj" />
    <Project Path="KafkaSyncService/EWP.SF.KafkaSync.DataAccess/EWP.SF.KafkaSync.DataAccess.csproj" />
  </Folder>
</Solution>
