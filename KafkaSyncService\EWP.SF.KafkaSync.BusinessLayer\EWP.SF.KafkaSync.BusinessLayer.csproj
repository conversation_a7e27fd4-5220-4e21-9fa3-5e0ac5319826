﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
<!-- Includes the LicenseModule DLL after the PostBuildEvent. -->
	<ItemGroup>
		<Reference Include="EWP.SF.LicenseModule">
			<HintPath>../../Lib/Windows/Release/EWP.SF.LicenseModule.dll</HintPath>
		</Reference>
	</ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.4" />
		
  </ItemGroup>
<!-- Package References -->
	<ItemGroup>
		<PackageReference Include="DynamicExpresso.Core" Version="2.18.0" />
		<PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="6.0.0-preview.4.21253.5" />
		<PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="9.0.4" />
		<PackageReference Include="Microsoft.VisualStudio.Services.Client" Version="19.225.1" />
		<PackageReference Include="MimeKit" Version="4.11.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="NLog.Database" Version="5.4.0" />
		<PackageReference Include="Otp.NET" Version="1.4.0" />
		<PackageReference Include="QRCoder" Version="1.6.0" />
		<PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
		<PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
		<PackageReference Include="System.Drawing.Common" Version="9.0.4" />
		<PackageReference Include="System.Formats.Asn1" Version="9.0.4" />
		<PackageReference Include="System.Management" Version="9.0.4" />
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
		<PackageReference Include="System.Runtime.Caching" Version="9.0.4" />
		<PackageReference Include="System.Text.Json" Version="9.0.4" />
		<PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
		<PackageReference Include="SystemTextJson.JsonDiffPatch" Version="2.0.0" />
		<PackageReference Include="WebPush" Version="1.0.12" />
		<PackageReference Include="Confluent.Kafka" Version="2.3.0" />
	</ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\EWP.SF.KafkaSync.DataAccess\EWP.SF.KafkaSync.DataAccess.csproj" />
    <ProjectReference Include="..\EWP.SF.KafkaSync.BusinessEntities\EWP.SF.KafkaSync.BusinessEntities.csproj" />
    <ProjectReference Include="..\..\SharedService\EWP.SF.Common\EWP.SF.Common.csproj" />
    <ProjectReference Include="..\..\SharedService\EWP.SF.Helper\EWP.SF.Helper.csproj" />
  </ItemGroup>

</Project>
