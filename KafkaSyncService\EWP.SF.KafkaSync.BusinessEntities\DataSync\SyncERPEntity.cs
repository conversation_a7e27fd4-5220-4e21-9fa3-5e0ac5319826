﻿namespace EWP.SF.KafkaSync.BusinessEntities;

public static class SyncERPEntity
{
	// Asset
	public const string FACILITY_SERVICE = "Facility";

	public const string FLOOR_SERVICE = "Floor";

	public const string WORKCENTER_SERVICE = "Workcenter";

	public const string PRODUCTION_LINE_SERVICE = "ProductionLine";

	public const string MACHINE_SERVICE = "Machine";

	public const string TOOLING_SERVICE = "Tooling";

	public const string TOOLING_TYPE_SERVICE = "ToolingType";

	public const string SENSOR_SERVICE = "Sensor";

	public const string ASSET_SHIFT_SERVICE = "AssetShifts";

	public const string ASSET_SHIFT_STATUS_SERVICE = "AssetShiftsStatus";

	// Employees
	public const string EMPLOYEE_SERVICE = "Employee";

	public const string CLOCKINOUT_SERVICE = "ClockInOut";

	public const string POSITION_SERVICE = "Position";

	public const string SKILL_CATALOG_SERVICE = "Skills";

	public const string BREAK_TYPE_SERVICE = "BreakTypes";

	public const string EMPLOYEE_SHIFT_SERVICE = "EmployeeShifts";

	public const string EMPLOYEE_SHIFT_STATUS_SERVICE = "EmployeeShiftsStatus";

	// General
	public const string ATTACHMENT_SERVICE = "Attachment";

	public const string ATTACHMENT_LINK_SERVICE = "AttachmentLink";

	public const string CALENDAR_SERVICE = "Calendar";

	public const string USER_SERVICE = "User";

	public const string NOTIFICATION_SERVICE = "Notification";

	//

	public const string SCHEDULE_SERVICE = "ProductionOrderScheduling";

	public const string ALLOCATION_SERVICE = "StockAllocation";

	public const string BIN_LOCATION_SERVICE = "BinLocation";

	public const string DEMAND_SERVICE = "Demand";

	public const string INVENTORY_SERVICE = "ItemGroup";

	public const string INVENTORY_STATUS_SERVICE = "InventoryStatus";

	public const string ITEM_SERVICE = "Item";

	public const string LOT_SERIAL_STATUS_SERVICE = "LotSerialStatus";

	public const string MATERIAL_ISSUE_SERVICE = "MaterialIssue";

	public const string MATERIAL_RETURN_SERVICE = "MaterialReturn";

	public const string MATERIAL_SCRAP_SERVICE = "MaterialScrap";

	public const string PRODUCT_SERVICE = "Products";

	public const string MACHINE_ISSUE_SERVICE = "MachineIssue";
	public const string LABOR_ISSUE_SERVICE = "LaborIssue";

	public const string PRODUCTION_ORDER_SERVICE = "ProductionOrder";

	public const string PRODUCTION_ORDER_CHANGE_STATUS_SERVICE = "ProductionOrderChangeStatus";

	public const string PRODUCT_RECEIPT_SERVICE = "ProductReceipt";

	public const string PRODUCT_RETURN_SERVICE = "ProductReturn";

	public const string PRODUCT_TRANSFER_SERVICE = "ProductTransfer";

	public const string STOCK_SERVICE = "Stock";

	public const string FULL_STOCK_SERVICE = "FullStock";

	public const string FULL_ALLOCATION_SERVICE = "FullStockAllocation";

	public const string SUPPLY_SERVICE = "Supply";

	public const string UNIT_MEASURE_SERVICE = "MeasureUnit";

	public const string WAREHOUSE_SERVICE = "Warehouse";

	public const string PROCESS_TYPE_SERVICE = "OperationSubtype";
	public const string PROCEDURE_SERVICE = "Procedure";
	public const string IOT_DATA_SIMULATOR_SERVICE = "IoTDataSimulator";
}

public static class SyncServiceExecType
{
	public const string Start = "Start";
	public const string Process = "Process";
	public const string End = "End";
}
