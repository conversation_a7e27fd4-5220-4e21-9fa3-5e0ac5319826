{
    // DEV [DEV]
    // "ConnectionStrings": {
    //     "Main": "ApplicationName=SF [DEV]{App}{User};Server=172.16.5.185;Port=3306;Database=ewp_sf_dev;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;",
    //     "Reports": "ApplicationName=SF [DEV]{App}{User};Server=172.16.5.185;Port=3306;Database=ewp_sf_dev;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;",
    //     "Logs": "ApplicationName=SF [DEV]{App}{User};Server=172.16.5.185;Port=3306;Database=ewp_sf_log;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;"
    // }
    "ConnectionStrings": {
    "Main": "ApplicationName=SF [DEV]KafkaSyncService;Server=172.16.5.117;Port=3306;Database=ewp_sf_dev;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;",
    "Reports": "ApplicationName=SF [DEV]KafkaSyncService;Server=172.16.5.117;Port=3306;Database=ewp_sf_dev;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;",
    "Logs": "ApplicationName=SF [DEV]KafkaSyncService;Server=172.16.5.117;Port=3306;Database=ewp_sf_log;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;"
  }
    // DEV (Dev_QA)
    /*
    "ConnectionStrings": {
        "Main": "ApplicationName=SF Dev_QA [DEV]{App}{User};Server=172.16.5.181;Port=3306;Database=ewp_sf_dev_qa;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;",
        "Reports": "ApplicationName=SF Dev_QA [DEV]{App}{User};Server=172.16.5.181;Port=3306;Database=ewp_sf_dev_qa;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;",
        "Logs": "ApplicationName=SF Dev_QA [DEV]{App}{User};Server=172.16.5.181;Port=3306;Database=ewp_sf_log;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;"
    }
    */
    // QA (QA)
    /*
    "ConnectionStrings": {
        "Main": "ApplicationName=SF QA [DEV]{App}{User};Server=172.16.5.192;Port=3306;Database=ewp_sf_qa;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;",
        "Reports": "ApplicationName=SF QA [DEV]{App}{User};Server=172.16.5.192;Port=3306;Database=ewp_sf_qa;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;",
        "Logs": "ApplicationName=SF QA [DEV]{App}{User};Server=172.16.5.192;Port=3306;Database=ewp_sf_log;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;"
    }
    */
    // QA (Sabores)
    /*
    "ConnectionStrings": {
        "Main": "ApplicationName=SF QA [DEV]{App}{User};Server=172.16.5.192;Port=3306;Database=ewp_sf;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;",
        "Reports": "ApplicationName=SF QA [DEV]{App}{User};Server=172.16.5.192;Port=3306;Database=ewp_sf;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;",
        "Logs": "ApplicationName=SF QA [DEV]{App}{User};Server=172.16.5.192;Port=3306;Database=ewp_sf_log;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;"
    }
    */
    // DEMO (Demo)
    /*
    "ConnectionStrings": {
        "Main": "ApplicationName=SF Demo [DEV]{App}{User};Server=172.16.5.209;Port=3306;Database=ewp_sf_demo;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;",
        "Reports": "ApplicationName=SF Demo [DEV]{App}{User};Server=172.16.5.209;Port=3306;Database=ewp_sf_demo;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;",
        "Logs": "ApplicationName=SF Demo [DEV]{App}{User};Server=172.16.5.209;Port=3306;Database=ewp_sf_log;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;"
    }
    */
}
