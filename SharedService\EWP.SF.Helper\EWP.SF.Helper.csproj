﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

	<!-- Package References -->
	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.DataProtection.Abstractions" Version="9.0.4" />
		<PackageReference Include="Microsoft.AspNetCore.Hosting" Version="2.3.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.4" />
		<PackageReference Include="NLog.Database" Version="5.4.0" />
		<PackageReference Include="MySqlConnector" Version="2.4.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="MimeKit" Version="4.11.0" />		
		<PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
	</ItemGroup>

	<!-- Project References -->
	<!-- <ItemGroup>
		<ProjectReference Include="..\EWP.SF.Common\EWP.SF.Common.csproj" />
	</ItemGroup> -->

</Project>
