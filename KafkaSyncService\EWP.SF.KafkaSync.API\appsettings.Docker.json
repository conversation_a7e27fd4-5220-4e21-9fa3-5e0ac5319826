{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  // "ConnectionStrings": {
  //   "Main": "ApplicationName=SF [DEV]KafkaSyncService;Server=************;Port=3306;Database=ewp_sf_dev;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;",
  //   "Reports": "ApplicationName=SF [DEV]KafkaSyncService;Server=************;Port=3306;Database=ewp_sf_dev;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;",
  //   "Logs": "ApplicationName=SF [DEV]KafkaSyncService;Server=************;Port=3306;Database=ewp_sf_dev;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;"
  // },
  "ConnectionStrings": {
    "Main": "ApplicationName=SF [DEV]KafkaSyncService;Server=172.16.5.117;Port=3306;Database=ewp_sf_dev;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;",
    "Reports": "ApplicationName=SF [DEV]KafkaSyncService;Server=172.16.5.117;Port=3306;Database=ewp_sf_dev;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;",
    "Logs": "ApplicationName=SF [DEV]KafkaSyncService;Server=172.16.5.117;Port=3306;Database=ewp_sf_log;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;"
  },
  "KafkaSettings": {
    "BootstrapServers": "kafka:29092",
    "GroupId": "ewp-sf-kafka-group",
    "Topics": {
      "KafkaSync": "kafka-sync-topic",
      "KafkaUpdates": "kafka-updates-topic"
    }
  },
  "AppSettings": {
    "B64Logo": "iVBORw0KGgoAAAANSUhEUgAAACsAAAAqCAMAAAAd31JXAAAAYFBMVEVpLF3ELlv05OuRLlvvMFnVMFr///+6LluFLVygLlsuLV/59vjdMVqqLlt7LFzX097nl6zBQ2ucbo/qytXGrcC1haBFVn94Qm1uc5UMMWTyWXrWXoCSnLSlUHeNXoPE5Fv51mydAAABhElEQVQ4y9WS6ZKCMBCE47gGTJSJOYDlev/H3J4Iblni8cctt0thAh/NTAf19brUJ7C7l3T4GHaz2RRPfhe2eKr3s8eHumb3D/U37Paubtnynt7FlrdstaY7vquq1n3vsv/JV637NpP3UyP3UXgpGpz9Fr5jSq4vemh36CBFVmvLCVDUWkewHldohK9ccCfCoXdkLSl9VqxNixPjmSTrBDbApRc2hMwoTcww0oOpcZVmfx3LcpT1SVhIXq+4NabFmmvD8PGqySahLHvcdkVmyXWds6o20IB1m49JNaSZpGGnL6yTHJxqRUNuooURy2iR8YJtlBYySwdhu2U2dGikCW4wmsc/bWWoIrNhztcuAgsnO0UM6PHomNu9YuuLTE4tBXhPpAO87XjNfs+SESU1zBUlC0IcdDyz1J/7HQQcmCW7nJpkUcVcxH3xm0MXlOahlc1gCS9vmZ1UdS7SwurgHOklBxJbM8kOYu8qn4txZq2d9ziL29xww1Lj+5X91WE/s+Qy/AN63TXLLKVmswAAAABJRU5ErkJggg=="
  }
}
