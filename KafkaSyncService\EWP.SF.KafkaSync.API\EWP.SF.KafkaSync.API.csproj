<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.4" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Confluent.Kafka" Version="2.3.0" />
  </ItemGroup>
<ItemGroup>
  <Compile Remove="Program.Docker.cs" />
</ItemGroup>
  <ItemGroup>

    <ProjectReference Include="..\EWP.SF.KafkaSync.DataAccess\EWP.SF.KafkaSync.DataAccess.csproj" />
    <ProjectReference Include="..\EWP.SF.KafkaSync.BusinessLayer\EWP.SF.KafkaSync.BusinessLayer.csproj" />
    <ProjectReference Include="..\..\SharedService\EWP.SF.Helper\EWP.SF.Helper.csproj" />
    <ProjectReference Include="..\EWP.SF.KafkaSync.BusinessEntities\EWP.SF.KafkaSync.BusinessEntities.csproj" />
    <ProjectReference Include="..\..\SharedService\EWP.SF.Common\EWP.SF.Common.csproj" />
  </ItemGroup>

</Project>
