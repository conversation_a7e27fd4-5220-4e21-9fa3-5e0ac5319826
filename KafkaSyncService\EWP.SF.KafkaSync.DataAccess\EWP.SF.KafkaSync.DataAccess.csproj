﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  
  <!-- Includes the ConnectionModule DLL after the PostBuildEvent. -->
	<ItemGroup>
		<Reference Include="EWP.SF.ConnectionModule">
			<HintPath>..\..\Lib\Windows\EWP.SF.ConnectionModule.dll</HintPath>
		</Reference>
	</ItemGroup>

<ItemGroup>
   <PackageReference Include="MySqlConnector" Version="2.4.0" />
		<PackageReference Include="NLog.Database" Version="5.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EWP.SF.KafkaSync.BusinessEntities\EWP.SF.KafkaSync.BusinessEntities.csproj" />
    <ProjectReference Include="..\..\SharedService\EWP.SF.Common\EWP.SF.Common.csproj" />
    <ProjectReference Include="..\..\SharedService\EWP.SF.Helper\EWP.SF.Helper.csproj" />
  </ItemGroup>

</Project>
