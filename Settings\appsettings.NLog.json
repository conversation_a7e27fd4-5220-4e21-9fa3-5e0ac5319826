{
    "NLog": {
        "autoReload": false,
        "throwConfigExceptions": true,
        "internalLogLevel": "Warn",
        "internalLogFile": "${basedir}/internal-nlog.txt",
        "extensions": [
            {
                "assembly": "NLog.Extensions.Logging"
            },
            {
                "assembly": "NLog.Web.AspNetCore"
            },
            {
                "assembly": "NLog.Database"
            }
        ],
        "time": {
            "type": "AccurateUTC"
        },
        "default-wrapper": {
            "type": "AsyncWrapper",
            "overflowAction": "Block",
            "batchSize": 50,
            "timeToSleepBetweenBatches": 200
        },
        "targets": {
            "database-logs": {
                "type": "Database",
                "dbProvider": "EWP.SF.ConnectionModule.EWP_Connection, EWP.SF.ConnectionModule",
                //"dbProvider": "MySqlConnector.MySqlConnection, MySqlConnector",
                "connectionString": "${configsetting:ConnectionStrings.Logs}",
                //"connectionString": "ApplicationName=Smart Factory Dev;Server=172.16.5.185;Port=3306;Database=ewp_sf_log;User=mesuser;Password=********************************************;Pooling=True;MinimumPoolSize=0;MaximumPoolSize=100;ConnectionIdleTimeout=180;ConnectionTimeout=15;DefaultCommandTimeout=30;",
                "keepConnection": "true",
                "commandText": "SP_SF_LOG",
                //"commandText": "INSERT INTO sf_logs (Id, DB) VALUES (UUID(), @Database);",
                //"commandText": "CALL SP_SF_LOG(@ExceptionId,@App,@Database,@Level,@Logger,@Message,@Exception,@StackTrace,@CreateUser,@CreateEmployee);",
                "commandType": "StoredProcedure", //"Text",
                "parameters": [
                    {
                        "name": "_ExceptionId",
                        "layout": "${mdlc:exception_id}"
                    },
                    {
                        "name": "_App",
                        "layout": "${var:App}"
                    },
                    {
                        "name": "_Database",
                        "layout": "${var:Database}"
                    },
                    {
                        "name": "_Level",
                        "layout": "${level}"
                    },
                    {
                        "name": "_Logger",
                        "layout": "${logger}"
                    },
                    {
                        "name": "_Message",
                        "layout": "${message}"
                    },
                    {
                        "name": "_Exception",
                        "layout": "${exception:tostring}"
                    },
                    {
                        "name": "_StackTrace",
                        "layout": "${stacktrace:format=Raw}"
                    },
                    {
                        "name": "_CreateUser",
                        "layout": "${mdlc:user_id}"
                    },
                    {
                        "name": "_CreateEmployee",
                        "layout": "${mdlc:employee_id}"
                    },
                    {
                        "name": "_DevUser",
                        "layout": "${var:DevUser}"
                    }
                ]
            },
            "file-logs": {
                "type": "File",
                "fileName": "Logs/app-log-${shortdate}.log",
                "archiveFileName": "Logs/archive/app-log-{#}.log",
                "archiveEvery": "Day",
                "archiveNumbering": "Rolling",
                "maxArchiveFiles": "7",
                "layout": {
                    "type": "JsonLayout",
                    "Attributes": [
                        {
                            "name": "timestamp",
                            "layout": "${date:format=o}"
                        },
                        {
                            "name": "level",
                            "layout": "${level}"
                        },
                        {
                            "name": "logger",
                            "layout": "${logger}"
                        },
                        {
                            "name": "message",
                            "layout": "${message:raw=true}"
                        },
                        {
                            "name": "properties",
                            "encode": false,
                            "layout": {
                                "type": "JsonLayout",
                                "includeallproperties": "true"
                            }
                        }
                    ]
                }
            },
            "console-logs": {
                "type": "ColoredConsole",
                "layout": "${level:uppercase=true:padding=-7} ${logger} ${message} ${exception:format=toString}",
                "rowHighlightingRules": [
                    {
                        "condition": "level == LogLevel.Error",
                        "foregroundColor": "Red"
                    },
                    {
                        "condition": "level == LogLevel.Warn",
                        "foregroundColor": "Yellow"
                    },
                    {
                        "condition": "level == LogLevel.Info",
                        "foregroundColor": "Green"
                    },
                    {
                        "condition": "level == LogLevel.Debug",
                        "foregroundColor": "Gray"
                    },
                    {
                        "condition": "level == LogLevel.Trace",
                        "foregroundColor": "DarkGray"
                    }
                ]
            }
        },
        "rules": [
            {
                "logger": "Microsoft.*",
                "levels": "Info",
                "writeTo": "console-logs,file-logs"
            },
            {
                "logger": "Microsoft.*",
                "levels": "Warn",
                "writeTo": "database-logs"
            },
            {
                "logger": "*",
                "minLevel": "Debug",
                "writeTo": "console-logs,file-logs,database-logs"
            }
        ]
    }
}
